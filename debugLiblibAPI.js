// LIBLIB API调试脚本
// 用于测试API连接和查看详细的请求/响应信息
// 使用方法：node debugLiblibAPI.js

import dotenv from 'dotenv';
import crypto from 'crypto';

// 加载环境变量
dotenv.config();

// LIBLIB API配置
const API_CONFIG = {
  baseUrl: 'https://openapi.liblibai.cloud',
  textToImageEndpoint: '/api/generate/webui/text2img',
  queryEndpoint: '/api/generate/webui/status',
  templateUuid: '6f7c4652458d4802969f8d089cf5b91f'
};

// 生成随机字符串（10位字母数字）
function generateRandomString(length = 10) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 生成签名（完全按照官方文档实现）
function generateSignature(uri, secretKey) {
  // 生成时间戳（毫秒）
  const timestamp = Date.now().toString();

  // 生成随机字符串（10位字母数字）
  const signatureNonce = generateRandomString(10);

  // 构建原文：URI + "&" + 时间戳 + "&" + 随机字符串
  const content = [uri, timestamp, signatureNonce].join('&');

  console.log('🔐 签名生成过程:');
  console.log('   URI:', uri);
  console.log('   时间戳:', timestamp);
  console.log('   随机字符串:', signatureNonce);
  console.log('   原文:', content);

  // 使用HMAC-SHA1生成签名
  const hmac = crypto.createHmac('sha1', secretKey);
  hmac.update(content, 'utf8');

  // 转换为URL安全的Base64（按照官方文档的Java实现）
  const signature = hmac.digest('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  console.log('   生成的签名:', signature);

  return {
    signature,
    timestamp,
    signatureNonce
  };
}

// 测试API连接
async function testLiblibAPI() {
  console.log('🧪 开始调试LIBLIB AI API连接');
  
  try {
    // 检查环境变量
    const accessKey = process.env.VITE_LIBLIB_ACCESS_KEY;
    const secretKey = process.env.VITE_LIBLIB_SECRET_KEY;
    
    if (!accessKey || !secretKey) {
      throw new Error('请在.env文件中设置LIBLIB API密钥');
    }
    
    console.log('✅ API密钥配置检查通过');
    console.log('   AccessKey长度:', accessKey.length);
    console.log('   SecretKey长度:', secretKey.length);
    console.log('   AccessKey前缀:', accessKey.substring(0, 8) + '...');
    
    // 准备请求
    const uri = API_CONFIG.textToImageEndpoint;
    const { signature, timestamp, signatureNonce } = generateSignature(uri, secretKey);
    
    const headers = {
      'Content-Type': 'application/json',
      'AccessKey': accessKey,
      'Signature': signature,
      'Timestamp': timestamp,
      'SignatureNonce': signatureNonce
    };
    
    const requestBody = {
      templateUuid: API_CONFIG.templateUuid,
      generateParams: {
        prompt: 'a cute brown bear sitting in front of a wooden house in the forest, children illustration style'
      }
    };
    
    console.log('\n📤 发送请求:');
    console.log('   URL:', `${API_CONFIG.baseUrl}${uri}`);
    console.log('   请求头:', JSON.stringify(headers, null, 2));
    console.log('   请求体:', JSON.stringify(requestBody, null, 2));
    
    // 发送请求
    const response = await fetch(`${API_CONFIG.baseUrl}${uri}`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    });
    
    console.log('\n📥 收到响应:');
    console.log('   状态码:', response.status);
    console.log('   状态文本:', response.statusText);
    console.log('   响应头:', Object.fromEntries(response.headers.entries()));
    
    // 读取响应内容
    const responseText = await response.text();
    console.log('   响应内容:', responseText);
    
    if (!response.ok) {
      console.log('\n❌ API请求失败');
      try {
        const errorData = JSON.parse(responseText);
        console.log('   错误详情:', JSON.stringify(errorData, null, 2));
      } catch (parseError) {
        console.log('   原始错误内容:', responseText);
      }
      return { success: false, error: `HTTP ${response.status}: ${responseText}` };
    }
    
    // 解析响应
    let responseData;
    try {
      responseData = JSON.parse(responseText);
      console.log('\n✅ 响应解析成功:');
      console.log('   解析后的数据:', JSON.stringify(responseData, null, 2));
    } catch (parseError) {
      console.log('\n❌ 响应解析失败:', parseError.message);
      return { success: false, error: '响应解析失败' };
    }
    
    // 检查响应格式和状态码
    if (responseData.code === 200 && responseData.data) {
      const taskId = responseData.data.generateUuid || responseData.data.uuid || responseData.data.id;

      if (taskId) {
        console.log('\n🎉 API调用成功!');
        console.log('   任务ID:', taskId);

        // 测试查询接口
        console.log('\n🔍 测试查询接口...');
        await testQueryAPI(accessKey, secretKey, taskId);

        return { success: true, taskId: taskId };
      } else {
        console.log('\n⚠️ 响应中未找到任务ID');
        console.log('   响应数据字段:', Object.keys(responseData.data || {}));
        return { success: false, error: '响应中缺少任务ID' };
      }
    } else {
      console.log('\n❌ API调用失败');
      console.log('   错误代码:', responseData.code);
      console.log('   错误信息:', responseData.msg || responseData.message);
      return { success: false, error: `API错误: ${responseData.msg || responseData.message}` };
    }
    
  } catch (error) {
    console.error('\n💥 调试过程中发生错误:', error.message);
    console.error('   错误堆栈:', error.stack);
    return { success: false, error: error.message };
  }
}

// 测试查询API
async function testQueryAPI(accessKey, secretKey, taskId) {
  try {
    const uri = API_CONFIG.queryEndpoint;
    const { signature, timestamp, signatureNonce } = generateSignature(uri, secretKey);

    const headers = {
      'Content-Type': 'application/json',
      'AccessKey': accessKey,
      'Signature': signature,
      'Timestamp': timestamp,
      'SignatureNonce': signatureNonce
    };

    const requestBody = {
      generateUuid: taskId
    };

    console.log('   查询URL:', `${API_CONFIG.baseUrl}${uri}`);
    console.log('   查询头:', JSON.stringify(headers, null, 2));
    console.log('   查询体:', JSON.stringify(requestBody, null, 2));

    const response = await fetch(`${API_CONFIG.baseUrl}${uri}`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    });

    console.log('   查询响应状态:', response.status);

    const responseText = await response.text();
    console.log('   查询响应内容:', responseText);

    if (response.ok) {
      try {
        const queryData = JSON.parse(responseText);
        console.log('   查询数据解析:', JSON.stringify(queryData, null, 2));
      } catch (parseError) {
        console.log('   查询响应解析失败:', parseError.message);
      }
    }

  } catch (error) {
    console.error('   查询API测试失败:', error.message);
  }
}

// 运行调试
testLiblibAPI()
  .then(result => {
    console.log('\n📊 调试结果:');
    if (result.success) {
      console.log('✅ API连接成功，可以正常使用');
      console.log('💡 现在可以运行完整的插画生成脚本');
    } else {
      console.log('❌ API连接失败:', result.error);
      console.log('🔧 请检查:');
      console.log('   1. API密钥是否正确');
      console.log('   2. 网络连接是否正常');
      console.log('   3. LIBLIB账户是否有足够余额');
    }
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n💥 调试脚本执行失败:', error);
    process.exit(1);
  });
