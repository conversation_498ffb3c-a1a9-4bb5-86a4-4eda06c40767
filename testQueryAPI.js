// 测试 LIBLIB AI 查询接口
import dotenv from 'dotenv';
import hmacsha1 from 'hmacsha1';
import randomString from 'string-random';

dotenv.config();

// 生成签名（按照官方示例）
const urlSignature = (url, secretKey) => {
  if (!url) return;
  const timestamp = Date.now();
  const signatureNonce = randomString(16);
  const str = `${url}&${timestamp}&${signatureNonce}`;
  const hash = hmacsha1(secretKey, str);
  let signature = hash
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=+$/, "");
  
  return {
    signature,
    timestamp,
    signatureNonce,
  };
};

// 测试不同的查询端点
async function testQueryEndpoints() {
  const accessKey = process.env.VITE_LIBLIB_ACCESS_KEY;
  const secretKey = process.env.VITE_LIBLIB_SECRET_KEY;
  
  if (!accessKey || !secretKey) {
    console.error('❌ API 密钥未配置');
    return;
  }
  
  // 先生成一个图片任务
  console.log('🎨 首先生成一个测试图片任务...');
  const generateUri = '/api/generate/webui/text2img';
  const { signature: genSig, timestamp: genTime, signatureNonce: genNonce } = urlSignature(generateUri, secretKey);
  
  const generateUrl = `https://openapi.liblibai.cloud${generateUri}?AccessKey=${accessKey}&Signature=${genSig}&Timestamp=${genTime}&SignatureNonce=${genNonce}`;
  
  try {
    const generateResponse = await fetch(generateUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        templateUuid: '5d7e67009b344550bc1aa6ccbfa1d7f4',
        generateParams: {
          prompt: 'test image'
        }
      })
    });
    
    const generateData = await generateResponse.json();
    console.log('生成响应:', generateData);
    
    if (generateData.code === 0 && generateData.data && generateData.data.generateUuid) {
      const taskId = generateData.data.generateUuid;
      console.log('✅ 任务创建成功，任务ID:', taskId);
      
      // 测试不同的查询端点
      const queryEndpoints = [
        '/api/generate/query',
        '/api/generate/webui/status',
        '/api/generate/status',
        '/api/query',
        '/api/generate/webui/query'
      ];
      
      for (const endpoint of queryEndpoints) {
        console.log(`\n🔍 测试查询端点: ${endpoint}`);
        await testQueryEndpoint(endpoint, taskId, accessKey, secretKey);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
      }
    } else {
      console.error('❌ 图片生成任务创建失败');
    }
  } catch (error) {
    console.error('❌ 生成图片任务失败:', error.message);
  }
}

// 测试单个查询端点
async function testQueryEndpoint(endpoint, taskId, accessKey, secretKey) {
  try {
    // 方法1: GET 请求 + URL 参数
    console.log(`   方法1: GET + URL参数`);
    const { signature: getSig, timestamp: getTime, signatureNonce: getNonce } = urlSignature(endpoint, secretKey);
    const getUrl = `https://openapi.liblibai.cloud${endpoint}?generateUuid=${taskId}&AccessKey=${accessKey}&Signature=${getSig}&Timestamp=${getTime}&SignatureNonce=${getNonce}`;
    
    const getResponse = await fetch(getUrl, { method: 'GET' });
    const getData = await getResponse.text();
    console.log(`   GET 响应 (${getResponse.status}):`, getData.substring(0, 200));
    
    // 方法2: POST 请求 + URL 参数 + 请求体
    console.log(`   方法2: POST + URL参数 + 请求体`);
    const { signature: postSig, timestamp: postTime, signatureNonce: postNonce } = urlSignature(endpoint, secretKey);
    const postUrl = `https://openapi.liblibai.cloud${endpoint}?AccessKey=${accessKey}&Signature=${postSig}&Timestamp=${postTime}&SignatureNonce=${postNonce}`;
    
    const postResponse = await fetch(postUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ generateUuid: taskId })
    });
    const postData = await postResponse.text();
    console.log(`   POST 响应 (${postResponse.status}):`, postData.substring(0, 200));
    
    // 方法3: POST 请求 + 请求头
    console.log(`   方法3: POST + 请求头`);
    const { signature: headerSig, timestamp: headerTime, signatureNonce: headerNonce } = urlSignature(endpoint, secretKey);
    
    const headerResponse = await fetch(`https://openapi.liblibai.cloud${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'AccessKey': accessKey,
        'Signature': headerSig,
        'Timestamp': headerTime.toString(),
        'SignatureNonce': headerNonce
      },
      body: JSON.stringify({ generateUuid: taskId })
    });
    const headerData = await headerResponse.text();
    console.log(`   Header 响应 (${headerResponse.status}):`, headerData.substring(0, 200));
    
  } catch (error) {
    console.error(`   ❌ 测试 ${endpoint} 失败:`, error.message);
  }
}

// 运行测试
console.log('🧪 开始测试 LIBLIB AI 查询接口');
testQueryEndpoints().catch(console.error);
