// LIBLIB AI API服务实现
// 基于LIBLIB AI官方API文档实现的图片生成服务

import apiKeyManager from './apiKeyManager.js';
import { IMAGE_PROMPT_TEMPLATE } from './promptTemplates.js';

class LiblibService {
  constructor() {
    // LIBLIB API配置（基于官方文档）
    this.baseUrl = 'https://openapi.liblibai.cloud';
    this.textToImageEndpoint = '/api/generate/webui/text2img/ultra';
    this.imageToImageEndpoint = '/api/generate/webui/img2img/ultra';
    this.queryEndpoint = '/api/generate/query';

    // 星流Star-3 Alpha文生图模板ID（来自官方文档）
    this.templateUuid = '5d7e67009b344550bc1aa6ccbfa1d7f4';
  }

  // 初始化API密钥
  initializeApiKeys(accessKey, secretKey) {
    apiKeyManager.initializeLiblib(accessKey, secretKey);
  }

  // 检查API密钥是否已初始化
  isApiKeyInitialized() {
    return apiKeyManager.isLiblibInitialized();
  }

  // 获取API密钥
  getApiKeys() {
    return apiKeyManager.getLiblibKeys();
  }

  // 生成签名
  async generateSignature(uri) {
    const { secretKey } = this.getApiKeys();

    if (!secretKey) {
      throw new Error('SecretKey未设置');
    }

    // 生成时间戳（毫秒）
    const timestamp = Date.now().toString();

    // 生成随机字符串
    const signatureNonce = this.generateUUID();

    // 构建原文
    const content = [uri, timestamp, signatureNonce].join('&');

    // 使用HMAC-SHA1生成签名
    const signature = await this.hmacSha1WebCrypto(content, secretKey);

    return {
      signature,
      timestamp,
      signatureNonce
    };
  }

  // HMAC-SHA1签名实现
  hmacSha1(content, key) {
    // 在浏览器环境中使用Web Crypto API
    return this.hmacSha1WebCrypto(content, key);
  }

  // 使用Web Crypto API实现HMAC-SHA1
  async hmacSha1WebCrypto(content, key) {
    const encoder = new TextEncoder();
    const keyData = encoder.encode(key);
    const contentData = encoder.encode(content);

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-1' },
      false,
      ['sign']
    );

    const signature = await crypto.subtle.sign('HMAC', cryptoKey, contentData);

    // 转换为URL安全的Base64并移除填充等号（按照官方文档要求）
    const uint8Array = new Uint8Array(signature);
    const base64 = btoa(String.fromCharCode(...uint8Array));
    // 转换为URL安全格式并移除填充等号
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  }

  // 生成UUID
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // 生成图片（异步模式）
  async generateImage(sceneDescription, ageRange) {
    try {
      if (!this.isApiKeyInitialized()) {
        throw new Error('LIBLIB API密钥未初始化，请先调用initializeApiKeys()');
      }

      // 构建提示词
      const prompt = IMAGE_PROMPT_TEMPLATE
        .replace('{scene_description}', sceneDescription)
        .replace('{age_range}', ageRange);

      console.log('使用LIBLIB AI生成图片，提示词:', prompt);

      // 第一步：发起图片生成任务
      const generateUuid = await this.submitGenerationTask(prompt);
      console.log('图片生成任务已提交，UUID:', generateUuid);

      // 第二步：轮询查询结果
      const imageUrl = await this.pollGenerationResult(generateUuid);
      console.log('图片生成完成，URL:', imageUrl);

      return imageUrl;
    } catch (error) {
      console.error('LIBLIB图片生成失败:', error);
      throw error;
    }
  }

  // 提交图片生成任务
  async submitGenerationTask(prompt) {
    const uri = this.textToImageEndpoint;
    const { signature, timestamp, signatureNonce } = await this.generateSignature(uri);
    const { accessKey } = this.getApiKeys();

    const headers = {
      'Content-Type': 'application/json',
      'AccessKey': accessKey,
      'Signature': signature,
      'Timestamp': timestamp,
      'SignatureNonce': signatureNonce
    };

    const requestBody = {
      templateUuid: this.templateUuid,
      generateParams: {
        prompt: prompt
      }
    };

    console.log('🔗 请求URL:', `${this.baseUrl}${uri}`);
    console.log('📤 请求头:', headers);
    console.log('📤 请求体:', JSON.stringify(requestBody, null, 2));

    const response = await fetch(`${this.baseUrl}${uri}`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    });

    console.log('📥 响应状态:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ 错误响应内容:', errorText);
      try {
        const error = JSON.parse(errorText);
        throw new Error(`提交生成任务失败: ${error.message || error.error || '未知错误'}`);
      } catch (parseError) {
        throw new Error(`提交生成任务失败: HTTP ${response.status} - ${errorText}`);
      }
    }

    const responseText = await response.text();
    console.log('📥 响应内容:', responseText);

    let data;
    try {
      data = JSON.parse(responseText);
    } catch (parseError) {
      throw new Error(`API响应解析失败: ${responseText}`);
    }

    console.log('📊 解析后的数据:', JSON.stringify(data, null, 2));

    if (!data.generateUuid && !data.uuid && !data.id && !data.taskId) {
      throw new Error(`API返回数据中缺少任务ID。响应数据: ${JSON.stringify(data)}`);
    }

    // 尝试不同的可能字段名
    const taskId = data.generateUuid || data.uuid || data.id || data.taskId;
    console.log('✅ 获取到任务ID:', taskId);

    return taskId;
  }

  // 轮询查询生成结果
  async pollGenerationResult(generateUuid, maxAttempts = 30, interval = 5000) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`查询生成结果，第${attempt}次尝试...`);

        const result = await this.queryGenerationResult(generateUuid);

        if (result.status === 'success') {
          if (result.imageUrl) {
            return result.imageUrl;
          } else {
            throw new Error('生成成功但未返回图片URL');
          }
        } else if (result.status === 'failed') {
          throw new Error(`图片生成失败: ${result.message || '未知原因'}`);
        } else {
          // 仍在生成中，等待后重试
          console.log(`图片生成中，${interval/1000}秒后重试...`);
          await this.sleep(interval);
        }
      } catch (error) {
        if (attempt === maxAttempts) {
          throw new Error(`查询生成结果失败，已重试${maxAttempts}次: ${error.message}`);
        }
        console.warn(`查询失败，${interval/1000}秒后重试:`, error.message);
        await this.sleep(interval);
      }
    }

    throw new Error(`图片生成超时，已等待${maxAttempts * interval / 1000}秒`);
  }

  // 查询单次生成结果
  async queryGenerationResult(generateUuid) {
    const uri = this.queryEndpoint;
    const { signature, timestamp, signatureNonce } = await this.generateSignature(uri);
    const { accessKey } = this.getApiKeys();

    const headers = {
      'AccessKey': accessKey,
      'Signature': signature,
      'Timestamp': timestamp,
      'SignatureNonce': signatureNonce
    };

    const url = `${this.baseUrl}${uri}?generateUuid=${generateUuid}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: headers
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`查询生成结果失败: ${error.message || '未知错误'}`);
    }

    return await response.json();
  }

  // 工具方法：延时
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 图生图功能（基于参考图像生成）
  async generateImageFromImage(referenceImageUrl, prompt, ageRange) {
    try {
      if (!this.isApiKeyInitialized()) {
        throw new Error('LIBLIB API密钥未初始化，请先调用initializeApiKeys()');
      }

      // 构建完整提示词
      const fullPrompt = IMAGE_PROMPT_TEMPLATE
        .replace('{scene_description}', prompt)
        .replace('{age_range}', ageRange);

      console.log('使用LIBLIB AI图生图功能，提示词:', fullPrompt);

      // 发起图生图任务
      const generateUuid = await this.submitImageToImageTask(referenceImageUrl, fullPrompt);
      console.log('图生图任务已提交，UUID:', generateUuid);

      // 轮询查询结果
      const imageUrl = await this.pollGenerationResult(generateUuid);
      console.log('图生图完成，URL:', imageUrl);

      return imageUrl;
    } catch (error) {
      console.error('LIBLIB图生图失败:', error);
      throw error;
    }
  }

  // 提交图生图任务
  async submitImageToImageTask(referenceImageUrl, prompt) {
    const uri = this.imageToImageEndpoint;
    const { signature, timestamp, signatureNonce } = await this.generateSignature(uri);
    const { accessKey } = this.getApiKeys();

    const headers = {
      'Content-Type': 'application/json',
      'AccessKey': accessKey,
      'Signature': signature,
      'Timestamp': timestamp,
      'SignatureNonce': signatureNonce
    };

    const requestBody = {
      templateUuid: this.templateUuid,
      generateParams: {
        prompt: prompt,
        init_images: [referenceImageUrl]
      }
    };

    const response = await fetch(`${this.baseUrl}${uri}`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`提交图生图任务失败: ${error.message || '未知错误'}`);
    }

    const data = await response.json();

    if (!data.generateUuid) {
      throw new Error('API返回数据中缺少generateUuid');
    }

    return data.generateUuid;
  }

  // 清除API密钥
  clearApiKeys() {
    apiKeyManager.clearLiblib();
  }

  // 测试API连接
  async testConnection() {
    try {
      if (!this.isApiKeyInitialized()) {
        throw new Error('API密钥未初始化');
      }

      // 使用简单的测试提示词
      const testPrompt = 'a cute bear, children illustration style';
      const imageUrl = await this.generateImage(testPrompt, '6-8岁');

      return {
        success: true,
        message: 'LIBLIB API连接成功',
        testImageUrl: imageUrl
      };
    } catch (error) {
      return {
        success: false,
        message: `LIBLIB API连接失败: ${error.message}`,
        error: error
      };
    }
  }

  // 获取API使用状态
  getApiStatus() {
    const isInitialized = this.isApiKeyInitialized();
    let hasAccessKey = false;
    let hasSecretKey = false;

    if (isInitialized) {
      const keys = this.getApiKeys();
      hasAccessKey = !!keys.accessKey;
      hasSecretKey = !!keys.secretKey;
    }

    return {
      isInitialized,
      baseUrl: this.baseUrl,
      templateUuid: this.templateUuid,
      hasAccessKey,
      hasSecretKey
    };
  }
}

// 创建单例实例
const liblibService = new LiblibService();

// 导出单例实例
export default liblibService;
